# Metaflow IA — Frontend
Este es el frontend de Metaflow IA, una aplicación web construida con Reflex que ofrece una experiencia fluida y moderna para interactuar con una inteligencia artificial conversacional.

🌐 Sitio en línea: https://metaflowia-teal-piano.reflex.run

🚀 Descripción
El frontend permite a los usuarios:

Registrarse o iniciar sesión (autenticación con JWT).
Ingresar como invitado para probar el chat.
Interactuar con una IA conversacional.
Visualizar su historial de chats.
El objetivo es ofrecer una experiencia ligera, responsiva y centrada en la interacción humano-IA, con foco en el aprendizaje, la exploración personal y la accesibilidad.

🛠️ Tecnologías utilizadas
Reflex (anteriormente Pynecone) — Framework frontend en Python.
React (bajo el capó) — Renderizado dinámico y componentes reutilizables.
Tailwind CSS (parcial) — Estilos modernos y responsive.
FastAPI — Backend de la aplicación (repositorio aparte).
JWT — Autenticación segura de usuarios.
MySQL — Base de datos para manejo de usuarios e historiales (vía SQLAlchemy).
📦 Instalación local
Cloná el repositorio: " git clone https://github.com/tu-usuario/metaflow-frontend.git cd metaflow-frontend "
install reflex | 3. run reflex
🔗 Conexión con el backend Este frontend se conecta con un backend desarrollado en FastAPI, alojado en:

Backend URL: https://metaflowia.onrender.com/

💬 Sobre el proyecto Metaflow IA es una iniciativa personal para explorar la integración entre tecnologías modernas de desarrollo web con IA conversacional, enfocada en experiencias creativas, humanas y conscientes.