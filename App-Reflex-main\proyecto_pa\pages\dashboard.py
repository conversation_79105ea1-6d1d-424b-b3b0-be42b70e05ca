# Esta página solo se muestra si se cumple lo siguiente:
# 1- El usuario inició sesión exitosamente
# 2- El usuario tenga token válido
 
import reflex as rx
from ..Auth import AuthState
from ..components.questionComponent import componente_pregunta

@rx.page(
        route='/dashboard',
        on_load=AuthState.verificar_token
        )
def pagina_dashboard() -> rx.Component:
    
    return rx.container(
        rx.cond( # Un cond no puede ejecutar funciones por sí sola
            AuthState.esta_cargando,
            rx.center(rx.text('Cargando...'), align='center'),
            rx.center(
                rx.container(
                rx.vstack(
                    rx.heading('Bienvenido', size='6', align='center'),
                    rx.button('Cerrar sesión', 
                              on_click=AuthState.cerrar_sesion,
                              color_scheme='red'
                              ),
                    rx.divider(),
                    componente_pregunta()          
                    )
                )
            )
        )
    )