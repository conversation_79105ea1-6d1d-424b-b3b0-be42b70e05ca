# Importamos reflex
import reflex as rx
from ..models.usuarios import Usuarios
from sqlmodel import select
import bcrypt
import jwt # Nos permite crear tarjetas de acceso
import datetime # Nos permite manejar el tiempo en python
from ..Auth import AuthState

LLAVE_SECRETA = 'RvFtp41PsmSgS46wHZVr' # Llave para generar los tokens

class EstadoLogin(rx.State):
    email: str = ''
    password: str = ''
    auth_token: str = rx.<PERSON>ie(name='auth_token', secure=True)
    cargando: bool = False
    error: bool = False
    
    # Este método se ejecuta cuando el input de email detecta un cambio
    @rx.event # Especifica que el método es un evento. Cuando se ejecute, se actualiza
    def asignarCorreo(self, correo_ingresado):
        self.email = correo_ingresado

    # Este método se ejecuta cuando el input de pass detecta un cambio
    @rx.event # Especifica que el método es un evento. Cuando se ejecute, se actualiza
    def asignarPassword(self, pass_ingresada):
        self.password = pass_ingresada

    def buscar_usuario(self):
        with rx.session() as sesion:
            usuario_registrado = sesion.exec( # Nos permite hacer querys con python
                select(Usuarios).where(Usuarios.email == self.email)
            ).first()
        return usuario_registrado
    
    def verificar_password(self, password_db):
        return bcrypt.checkpw(self.password.encode(), password_db.encode()) # Retorna True si el hass pertenece a la pass ingresada, False si no
    
    @rx.event
    def iniciar_sesion(self):
        self.cargando = True
        self.error = False
        yield # Si lo ejecutamos sin nada que regresar, la UI se actualiza
        usuario = self.buscar_usuario() # Nos retorna None si el usuario no existe y una lista si existe
        if usuario and self.verificar_password(usuario.password): # Nos indica si el usuario está registrado en la DB
            print('Inicio de sesión existoso')
            # Cuando el usuario haya iniciado sesión de manera satisfactoria entonces le creamos una credencial de acceso
            datos_token = {
                'user_id': usuario.id,
                'user_name': usuario.nombre,
                'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1) # La credencial tiene una expiración de 5 min
            }
            # Creamos credencial
            token = jwt.encode(datos_token, LLAVE_SECRETA, algorithm='HS256') # Codifica info en una credencial
            # print(f'Se creó el token: {token}')
            yield AuthState.guardar_token(token)
            self.cargando = False
            return rx.redirect('/dashboard')
        else: # El usuario no existe en la DB
            print('Correo o contraseña inválidos')
            self.error = True
            self.password = ''
            self.cargando = False
            yield

    @rx.event
    def mostrarInfo(self):
        print(self.email)
        print(self.password)
        print('-----------------------------------')
        if self.email == '<EMAIL>' and self.password == '123':
            print('Bienvenido admin')
            self.email = ''
            self.password = ''
        else:
            print('Correo o contraseña incorrecta')
            self.email = ''
            self.password = ''

@rx.page(
        route='/login',
        on_load=AuthState.verificar_usuario
        )
@rx.page(route = '/login', title = 'Iniciar sesión')
def pagina_login() -> rx.Component:
    return rx.container(
        rx.center(
            rx.vstack(
                rx.heading('Inicio de sesión'),
                rx.input(
                    placeholder = 'Correo',
                    type = 'email',
                    value = EstadoLogin.email,
                    on_change = EstadoLogin.asignarCorreo
                ),
                rx.input(
                    placeholder = 'Contraseña',
                type='password',
                    value = EstadoLogin.password,
                    on_change = EstadoLogin.asignarPassword
                ),
                rx.cond(
                    EstadoLogin.cargando,
                    #rx.spinner(
                    #    size='3',
                    #    align='center'
                    rx.button(
                        rx.spinner(loading=True),
                        'Iniciar sesión',
                        disabled=True
                    ),
                    rx.button(
                        'Iniciar sesión',
                        on_click = EstadoLogin.iniciar_sesion
                    )
                ),
                rx.cond(
                    EstadoLogin.error,
                    rx.callout(
                        "Usuario o contraseña incorrecto",
                        icon="triangle_alert",
                        color_scheme="red",
                        role="alert",
                    )
                )
            )
        )
    )