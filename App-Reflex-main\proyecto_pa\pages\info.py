import reflex as rx

# Modifica el estado de una varaiable
class ContadorEstado(rx.State):
    contador: int = 0 

    @rx.event # Sirve para que cuando se ejecute 'incrementar_contador', se actualice sola la página
    def incrementar_contador(self):
        self.contador += 1 
    
    @rx.event
    def decrementar_contador(self):
        self.contador -= 1

@rx.page(route = '/informacion', title = 'About Us')
def pagina_info() -> rx.Component:
    # Creamos un container o espacio de trabajo
    return rx.container(
        rx.center(
            rx.vstack(
                rx.text('Cambio de estado', size = '4'),
                rx.text(ContadorEstado.contador, size = '9', align = 'center'),
                rx.button(
                    'Incrementar',
                    on_click = ContadorEstado.incrementar_contador
                ),
                rx.button(
                    'Decrementar',
                    on_click = ContadorEstado.decrementar_contador
                )
            )
        )
    )
