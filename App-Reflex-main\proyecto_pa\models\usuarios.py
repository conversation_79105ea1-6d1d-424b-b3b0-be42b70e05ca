# Controla la info entre la app y la base de datos con la tabla "usuarios"
import reflex as rx
from sqlmodel import Field # La librería contiene funciones extra para darle formato a la tabla
import bcrypt # Importamos el módulo para 'Hashear' las contraseñas

# Creamos una clase de controla como se vería una tabla en la base de datos
class Usuarios(rx.Model, table=True):
    __tablename__ = 'usuarios'
    id: int = Field(default=None, primary_key=True)
    nombre: str
    email: str
    password: str

    @classmethod
    def crear_usuario(cls, nombre_usuario, correo_usuario, pass_usuario):
        pass_codificada = pass_usuario.encode() # .encode onvierte la contraseña en bytes para poderla hashear
        pass_hasheada = bcrypt.hashpw(pass_codificada, bcrypt.gensalt()) # Pasamos la contraseña por proceso de Hasheo
        return cls(nombre=nombre_usuario, email=correo_usuario, password=pass_hasheada.decode()) # .decode convierte de bytes a str


