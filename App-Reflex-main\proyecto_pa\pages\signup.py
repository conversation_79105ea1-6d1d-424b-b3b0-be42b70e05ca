# Pedir nombre, correo y contraseña

# Importamos reflex
import reflex as rx
from ..models.usuarios import Usuarios
from sqlmodel import select 
from ..Auth import AuthState


class EstadoSignup(rx.State):
    email: str = ''
    password: str = ''
    nombre: str = ''
    
    # Este método se ejecuta cuando el input de email detecta un cambio
    @rx.event # Especifica que el método es un evento. Cuando se ejecute, se actualiza
    def asignarCorreo(self, correo_ingresado):
        self.email = correo_ingresado

    # Este método se ejecuta cuando el input de pass detecta un cambio
    @rx.event # Especifica que el método es un evento. Cuando se ejecute, se actualiza
    def asignarPassword(self, pass_ingresada):
        self.password = pass_ingresada

    @rx.event
    def asignarNombre(self, nombre_ingresado):
        self.nombre = nombre_ingresado

    # Este método nos ayuda a buscar una cuenta a través de un correo para que no estén repetidos
    def buscar_usuario(self):
        with rx.session() as sesion:
            usuario_registrado = sesion.exec( # Nos permite hacer querys con python
                select(Usuarios).where(Usuarios.email == self.email)
            ).first()
        #print(usuario_registrado)
        return usuario_registrado

    @rx.event
    def registrar_cuenta(self):
        usuario_registrado = self.buscar_usuario() 
        if usuario_registrado: 
            print('El usuario ya está registrado')
            self.email = ''
        else:
            nuevo_usuario = Usuarios.crear_usuario(self.nombre, self.email, self.password)
            with rx.session() as sesion: # Maneja una sesión automática para transferir datos
                sesion.add(nuevo_usuario) # Agrega al nuevo usuario creado
                sesion.commit() # Guarda los cambios en la base de datos. Ya sea agregarlos, editarlos, borrarlos
            return rx.redirect('/login') # Re dirige al usuario a otra página

    @rx.event
    def mostrarInfo(self):
        print(self.email)
        print(self.password)
        print(self.nombre)
        print('-----------------------------------')
        if self.email == '<EMAIL>' and self.password == '123':
            print('Bienvenido admin')
            self.email = ''
            self.password = ''
        else:
            print('Correo o contraseña incorrecta')
            self.email = ''
            self.password = ''

@rx.page(
        route='/signup',
        on_load=AuthState.verificar_usuario
         )
@rx.page(route = '/signup', title = 'Regístrate')
def pagina_signup() -> rx.Component:
    return rx.container(
        rx.center(
            rx.vstack(
                rx.heading('Regístrate'),
                rx.input(
                     placeholder = 'Nombre completo',
                     type = 'str',
                     value = EstadoSignup.nombre,
                     on_change = EstadoSignup.asignarNombre
                ),
                rx.input(
                    placeholder = 'Correo',
                    type = 'email',
                    value = EstadoSignup.email,
                    on_change = EstadoSignup.asignarCorreo
                ),
                rx.input(
                    placeholder = 'Contraseña',
                    type='password',
                    value = EstadoSignup.password,
                    on_change = EstadoSignup.asignarPassword
                ),
                rx.button(
                    'Crear cuenta',
                    on_click = EstadoSignup.registrar_cuenta
                )
            )
        )
    )