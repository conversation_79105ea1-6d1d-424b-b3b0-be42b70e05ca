import reflex as rx
import jwt
from ..models.preguntas import Preguntas
from ..Auth import AuthState

LLAVE_SECRETA = 'RvFtp41PsmSgS46wHZVr' # Llave para generar los tokens

class EstadoPregunta(rx.State):
    pregunta: str = ''
    detalles: str = ''
    informacion_form: dict = {}
    mensaje_exito: str = ''
    mostrar_mensaje: bool = False

    # Este método nos ayudará a recibir la info que el usuario haya agregado en el form
    @rx.event
    def obtener_pregunta(self, form_data: dict):
        id_usuario = str(AuthState.user_id)
        print(id_usuario)
        self.pregunta = form_data['pregunta']
        self.detalles = form_data['detalles']

        nueva_pregunta = Preguntas.crear_pregunta(usuario_id=id_usuario, pregunta=self.pregunta, detalles=self.detalles)

        with rx.session() as sesion:
            sesion.add(nueva_pregunta) # Se agrega una nueva pregunta
            sesion.commit() # Se guardan los cambios en la db


def form_field(label: str, placeholder: str, type: str, name: str) -> rx.Component:
    return rx.form.field(
        rx.flex(
            rx.form.label(label),
            rx.form.control(
                rx.input(
                    placeholder=placeholder, type=type
                ),
                as_child=True,
            ),
            direction="column",
            spacing="1",
        ),
        name=name,
        width="100%",
    )

def componente_pregunta() -> rx.Component:
    return rx.card(
        rx.flex(
            rx.hstack(
                rx.badge(
                    rx.icon(tag="circle_help", size=32),
                    color_scheme="blue",
                    radius="full",
                    padding="0.65rem",
                ),
                rx.vstack(
                    rx.heading(
                        "¿Tienes preguntas?",
                        size="4",
                        weight="bold",
                    ),
                    rx.text(
                        "Compártelas con la comunidad",
                        size="2",
                    ),
                    spacing="1",
                    height="100%",
                ),
                height="100%",
                spacing="4",
                align_items="center",
                width="100%",
            ),
            rx.form.root(
                rx.flex(
                    rx.flex(
                        form_field(
                            "¿Cuál es tu pregunta?",
                            "Ingresa tu pregunta",
                            "text",
                            "pregunta",
                        ),
                        spacing="3",
                        flex_direction=[
                            "column",
                            "row",
                            "row",
                        ],
                    ),
                    rx.flex(
                        rx.text(
                            "Detalles",
                            style={
                                "font-size": "15px",
                                "font-weight": "500",
                                "line-height": "35px",
                            },
                        ),
                        rx.text_area(
                            placeholder="Pon más detalle de la pregunta",
                            name="detalles",
                            resize="vertical",
                        ),
                        direction="column",
                        spacing="1",
                    ),
                    rx.form.submit(
                        rx.button("Enviar"),
                        as_child=True,
                    ),
                    direction="column",
                    spacing="2",
                    width="100%",
                ),
                on_submit=EstadoPregunta.obtener_pregunta,
                reset_on_submit=False,
            ),
            width="100%",
            direction="column",
            spacing="4",
        ),
        size="3",
    )