# Controla los inicios de sesión y las cookies
import reflex as rx
import jwt 

LLAVE_SECRETA = 'RvFtp41PsmSgS46wHZVr' # Llave para generar los tokens

class AuthState(rx.State):
    auth_token: str = rx.<PERSON><PERSON>(name='auth_token', secure=True) # Indica que auth_token será una cookie 
    esta_cargando: bool = False
    user_id: str = ''

    @rx.event
    def guardar_token(self, token: str):
        self.auth_token = token

    # Para que esto funcione en reflex y lo podamos utilizar como variable (booleana) fuera de la clase,
    # se lo tenemos que especificar
    @rx.event 
    def verificar_token(self): # Verifica la autenticidad del token
      # Cuando se empiece a ejecutar empieza la carga
      self.esta_cargando = True
      if not self.auth_token or len(self.auth_token) == 0: # Existe la variable?
            print('El ususario no tiene token')
            # Termina la carga porque ya verificamos el token
            self.esta_cargando = False
            return rx.redirect('/login')
      else: # Si existe el token: 
        try: # El token puede fallar si ya caducó o si es inválido
            token_decodificado = jwt.decode(self.auth_token, LLAVE_SECRETA, algorithms=['HS256']) # el método decode() recibe el token, la llave secreta y el argoritmo de cifrado
            print(token_decodificado)
            self.user_id = str(token_decodificado['user_id'])
            self.esta_cargando = False
            # return True
        except jwt.ExpiredSignatureError: # Si el token ya caducó
            print('El token caducó')
            self.esta_cargando = False
            return rx.redirect('/login')
        except jwt.InvalidTokenError: # Si el token es inválido
            print('El token es inválido o viene mal firmado')
            self.esta_cargando = False
            return rx.redirect('/login')
        
    @rx.event
    def verificar_usuario(self):
        if not self.auth_token or len(self.auth_token) == 0: # Existe la variable?
            print('No tienes token, quedate en Iniciar Sesion')
        else:
            try:
                token_decodificado = jwt.decode(self.auth_token, LLAVE_SECRETA, algorithms=['HS256']) # el método decode() recibe el token, la llave secreta y el argoritmo de cifrado
                return rx.redirect('/dashboard')
            except jwt.PyJWKError: # Ayuda a verificar si algo falló con el token
                print('Tu token está mal, quédate aquí')

    @rx.event
    def cerrar_sesion(self):
        print('Cerrando sesión...')
        self.auth_token = '' # Esto elimina el contenido del atributo "auth_token"
        rx.remove_cookie('auth_token') # Eliminamos la cookie
        return rx.redirect('/login') # Cuando borramos la cookie mandamos al usuario al login
    