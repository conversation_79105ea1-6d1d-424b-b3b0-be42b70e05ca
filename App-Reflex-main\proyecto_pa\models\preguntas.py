# Va todo lo relacionado a las preguntas y su tabla
import reflex as rx
from sqlmodel import Field, Relationship, SQLModel

class Preguntas(rx.Model, table = True):
    id: int = Field(default=None, primary_key=True, nullable=False)
    usuario_id: int = Field(foreign_key='usuario.id', nullable=False)
    pregunta: str 
    detalles: str

    # Creamos un método que permita ingresar las preguntas en la db
    @classmethod
    def crear_pregunta(cls, id_usuario, pregunta, detalles):
        return cls(usuario_id=id_usuario, pregunta=pregunta, detalles=detalles)
    