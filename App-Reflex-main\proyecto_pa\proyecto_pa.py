"""Welcome to Reflex! This file outlines the steps to create a basic app."""
# Contraseña supabase: DuDDO4P7uWlqIkWg
# URL Supabase: postgresql://postgres.ispfghqjuhwegzxajqxu:[YOUR-PASSWORD]@aws-0-us-east-2.pooler.supabase.com:6543/postgres

import reflex as rx

from rxconfig import config
from .pages.info import pagina_info
from .pages.login import pagina_login
from .pages.signup import pagina_signup
from .pages.dashboard import pagina_dashboard

def index() -> rx.Component:
    # Welcome Page (Index)
    return rx.container(
        rx.color_mode.button(position="top-right"),
        rx.vstack(
            rx.heading("Mi primera app web!", size="9"),
            rx.text(
                "Get started by editing ",
                rx.code(f"{config.app_name}/{config.app_name}.py"),
                size="5",
            ),
            rx.link(
                rx.button("Check out our docs!"),
                href="https://reflex.dev/docs/getting-started/introduction/",
                is_external=True,
            ),
            spacing="5",
            justify="center",
            min_height="85vh",
        ),
        rx.logo(),
    )

# Inicia la app
app = rx.App()

# Agrega una página index
app.add_page(index)
app.add_page(pagina_info)
app.add_page(pagina_login)
app.add_page(pagina_signup)
app.add_page(pagina_dashboard, route='/dashboard', title='Dashboard')